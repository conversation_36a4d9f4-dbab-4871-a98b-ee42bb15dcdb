package cn.iocoder.yudao.module.wbclass.dal.dataobject.course;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 订单课程关联 DO
 * 
 * 用于管理订单与课程的多对多关联关系，解耦订单表与课程的直接关联
 *
 * <AUTHOR>
 */
@TableName("edusys_wbclass_order_course_relation")
@KeySequence("edusys_wbclass_order_course_relation_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WbClassOrderCourseRelationDO extends BaseDO {

    /**
     * 关联ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称（冗余字段，便于查询）
     */
    private String courseName;

    /**
     * 排序（同一订单下课程的排序）
     */
    private Integer sort;

    /**
     * 状态：1-正常，2-已删除
     */
    private Integer status;

}
