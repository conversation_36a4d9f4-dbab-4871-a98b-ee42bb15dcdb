package cn.iocoder.yudao.module.wbclass.service.progress;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.collection.YudaoCollectionUtils;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.courselesson.WbClassCourseLessonDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.courselesson.WbClassCourseLessonExerciseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.progress.WbClassUserExerciseProgressDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.progress.WbClassUserLessonProgressDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.courselesson.WbClassCourseLessonExerciseMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.courselesson.WbClassCourseLessonMapper;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import cn.iocoder.yudao.module.wbclass.dal.mysql.progress.WbClassUserExerciseProgressMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.progress.WbClassUserLessonProgressMapper;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 学习进度 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WbClassLearningProgressServiceImpl implements WbClassLearningProgressService {

    @Resource
    private WbClassCourseLessonMapper courseLessonMapper;

    @Resource
    private WbClassCourseLessonExerciseMapper courseLessonExerciseMapper;

    @Resource
    private WbClassUserLessonProgressMapper userLessonProgressMapper;

    @Resource
    private WbClassUserExerciseProgressMapper userExerciseProgressMapper;

    @Resource
    private WbClassCourseProductSkuService skuService;

    @Resource
    private WbClassOrderCourseRelationService orderCourseRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeLearningProgress(Long orderId, Long skuId, Long memberId) {
        log.info("开始初始化学习进度: orderId={}, skuId={}, memberId={}", orderId, skuId, memberId);

        try {
            // 优先从订单-课程关联表获取课程列表
            List<WbClassCourseDO> courses = orderCourseRelationService.getCoursesByOrderId(orderId);

            // 如果关联表中没有数据，回退到从SKU获取课程（向后兼容）
            if (CollUtil.isEmpty(courses) && skuId != null) {
                log.info("订单-课程关联表中无数据，从SKU获取课程: orderId={}, skuId={}", orderId, skuId);
                courses = skuService.getCoursesBySkuId(skuId);
            }

            if (CollUtil.isEmpty(courses)) {
                log.warn("没有找到关联课程，无需初始化学习进度: orderId={}, skuId={}", orderId, skuId);
                return;
            }

            // 为每个课程初始化学习进度
            for (WbClassCourseDO course : courses) {
                // 1. 初始化课节进度
                initializeLessonProgressForCourse(orderId, course.getId(), memberId);

                // 2. 初始化练习进度
                initializeExerciseProgressForCourse(orderId, course.getId(), memberId);
            }

            log.info("学习进度初始化完成: orderId={}, skuId={}, memberId={}, 课程数={}",
                    orderId, skuId, memberId, courses.size());
        } catch (Exception e) {
            log.error("学习进度初始化失败: orderId={}, skuId={}, memberId={}", orderId, skuId, memberId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeLessonProgress(Long orderId, Long skuId, Long memberId) {
        // 优先从订单-课程关联表获取课程列表
        List<WbClassCourseDO> courses = orderCourseRelationService.getCoursesByOrderId(orderId);

        // 如果关联表中没有数据，回退到从SKU获取课程（向后兼容）
        if (CollUtil.isEmpty(courses) && skuId != null) {
            courses = skuService.getCoursesBySkuId(skuId);
        }

        for (WbClassCourseDO course : courses) {
            initializeLessonProgressForCourse(orderId, course.getId(), memberId);
        }
    }

    private void initializeLessonProgressForCourse(Long orderId, Long courseId, Long memberId) {
        // 检查是否已经初始化过
        List<WbClassUserLessonProgressDO> existingProgress = 
            userLessonProgressMapper.selectListByOrderCourseAndMember(orderId, courseId, memberId);
        
        if (CollUtil.isNotEmpty(existingProgress)) {
            log.info("课节进度已存在，跳过初始化: orderId={}, courseId={}, memberId={}", orderId, courseId, memberId);
            return;
        }

        // 获取课程的所有课节，按排序号排序
        List<WbClassCourseLessonDO> lessons = courseLessonMapper.selectListByCourseId(courseId);
        if (CollUtil.isEmpty(lessons)) {
            log.warn("课程没有课节，无需初始化课节进度: courseId={}", courseId);
            return;
        }

        // 按排序号排序
        lessons.sort(Comparator.comparing(WbClassCourseLessonDO::getSort, Comparator.nullsLast(Comparator.naturalOrder())));

        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < lessons.size(); i++) {
            WbClassCourseLessonDO lesson = lessons.get(i);
            
            // 判断课节的解锁状态
            boolean shouldUnlock = shouldUnlockLesson(lesson, i);
            
            WbClassUserLessonProgressDO progress = WbClassUserLessonProgressDO.builder()
                    .orderId(orderId)
                    .courseId(courseId)
                    .lessonId(lesson.getId())
                    .memberId(memberId)
                    .unlockStatus(shouldUnlock ? 1 : 0) // 1-已解锁，0-未解锁
                    .unlockTime(shouldUnlock ? now : null)
                    .learningStatus(0) // 0-未开始
                    .learnDuration(0)
                    .completionRate(0)
                    .build();

            userLessonProgressMapper.insert(progress);
            
            log.debug("创建课节进度记录: lessonId={}, unlock={}", lesson.getId(), shouldUnlock);
        }
        
        log.info("课节进度初始化完成: orderId={}, courseId={}, memberId={}, 课节数={}", 
                orderId, courseId, memberId, lessons.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeExerciseProgress(Long orderId, Long skuId, Long memberId) {
        // 优先从订单-课程关联表获取课程列表
        List<WbClassCourseDO> courses = orderCourseRelationService.getCoursesByOrderId(orderId);

        // 如果关联表中没有数据，回退到从SKU获取课程（向后兼容）
        if (CollUtil.isEmpty(courses) && skuId != null) {
            courses = skuService.getCoursesBySkuId(skuId);
        }

        for (WbClassCourseDO course : courses) {
            initializeExerciseProgressForCourse(orderId, course.getId(), memberId);
        }
    }

    private void initializeExerciseProgressForCourse(Long orderId, Long courseId, Long memberId) {
        // 检查是否已经初始化过
        List<WbClassUserExerciseProgressDO> existingProgress = 
            userExerciseProgressMapper.selectListByOrderCourseAndMember(orderId, courseId, memberId);
        
        if (CollUtil.isNotEmpty(existingProgress)) {
            log.info("练习进度已存在，跳过初始化: orderId={}, courseId={}, memberId={}", orderId, courseId, memberId);
            return;
        }

        // 获取课程的所有练习
        List<WbClassCourseLessonExerciseDO> exercises = courseLessonExerciseMapper.selectListByCourseId(courseId);
        if (CollUtil.isEmpty(exercises)) {
            log.warn("课程没有练习，无需初始化练习进度: courseId={}", courseId);
            return;
        }

        // 获取已解锁的课节列表
        List<WbClassUserLessonProgressDO> unlockedLessons = 
            userLessonProgressMapper.selectUnlockedLessons(orderId, courseId, memberId);
        Map<Long, WbClassUserLessonProgressDO> unlockedLessonMap = 
            YudaoCollectionUtils.convertMap(unlockedLessons, WbClassUserLessonProgressDO::getLessonId);

        // 按课节ID和排序号分组练习
        Map<Long, List<WbClassCourseLessonExerciseDO>> exercisesByLesson = 
            YudaoCollectionUtils.convertMultiMap(exercises, WbClassCourseLessonExerciseDO::getLessonId);

        LocalDateTime now = LocalDateTime.now();
        int totalCreated = 0;

        for (Map.Entry<Long, List<WbClassCourseLessonExerciseDO>> entry : exercisesByLesson.entrySet()) {
            Long lessonId = entry.getKey();
            List<WbClassCourseLessonExerciseDO> lessonExercises = entry.getValue();
            
            // 课节必须已解锁才能创建练习进度
            boolean lessonUnlocked = unlockedLessonMap.containsKey(lessonId);
            
            // 按排序号排序
            lessonExercises.sort(Comparator.comparing(WbClassCourseLessonExerciseDO::getSort, 
                    Comparator.nullsLast(Comparator.naturalOrder())));

            for (int i = 0; i < lessonExercises.size(); i++) {
                WbClassCourseLessonExerciseDO exercise = lessonExercises.get(i);
                
                // 判断练习的解锁状态
                boolean shouldUnlock = lessonUnlocked && shouldUnlockExercise(exercise, i);
                
                WbClassUserExerciseProgressDO progress = WbClassUserExerciseProgressDO.builder()
                        .orderId(orderId)
                        .courseId(courseId)
                        .lessonId(lessonId)
                        .exerciseId(exercise.getId())
                        .memberId(memberId)
                        .unlockStatus(shouldUnlock ? 1 : 0) // 1-已解锁，0-未解锁
                        .unlockTime(shouldUnlock ? now : null)
                        .learningStatus(0) // 0-未学习
                        .submissionCount(0)
                        .hasSubmission(0)
                        .build();

                userExerciseProgressMapper.insert(progress);
                totalCreated++;
                
                log.debug("创建练习进度记录: exerciseId={}, lessonId={}, unlock={}", 
                        exercise.getId(), lessonId, shouldUnlock);
            }
        }
        
        log.info("练习进度初始化完成: orderId={}, courseId={}, memberId={}, 练习数={}", 
                orderId, courseId, memberId, totalCreated);
    }

    @Override
    public void checkAndUnlockNextLesson(Long orderId, Long courseId, Long memberId, Long completedLessonId) {
        // TODO: 实现检查并解锁下一个课节的逻辑
        // 当课节完成后，检查解锁条件，解锁下一个课节
        log.info("检查并解锁下一个课节: orderId={}, courseId={}, memberId={}, completedLessonId={}", 
                orderId, courseId, memberId, completedLessonId);
    }

    @Override
    public void checkAndUnlockNextExercise(Long orderId, Long courseId, Long memberId, Long lessonId, Long completedExerciseId) {
        // TODO: 实现检查并解锁下一个练习的逻辑  
        // 当练习完成后，检查解锁条件，解锁下一个练习
        log.info("检查并解锁下一个练习: orderId={}, courseId={}, memberId={}, lessonId={}, completedExerciseId={}", 
                orderId, courseId, memberId, lessonId, completedExerciseId);
    }

    /**
     * 判断课节是否应该解锁
     * 
     * @param lesson 课节对象
     * @param index 课节在列表中的索引（从0开始）
     * @return 是否应该解锁
     */
    private boolean shouldUnlockLesson(WbClassCourseLessonDO lesson, int index) {
        // 如果课节配置为不上锁，直接解锁
        if (lesson.getAutoUnlock() != null && lesson.getAutoUnlock() == 0) {
            return true;
        }
        
        // 第一个课节默认解锁
        if (index == 0) {
            return true;
        }
        
        // 其他课节根据解锁类型判断
        // 这里先简化为：只有第一个课节默认解锁，其他课节需要顺序解锁
        return false;
    }

    /**
     * 判断练习是否应该解锁
     * 
     * @param exercise 练习对象
     * @param index 练习在课节中的索引（从0开始）
     * @return 是否应该解锁
     */
    private boolean shouldUnlockExercise(WbClassCourseLessonExerciseDO exercise, int index) {
        // 如果练习配置为不上锁，直接解锁
        if (exercise.getAutoUnlock() != null && exercise.getAutoUnlock() == 0) {
            return true;
        }
        
        // 每个课节的第一个练习默认解锁（如果课节已解锁）
        if (index == 0) {
            return true;
        }
        
        // 其他练习需要顺序解锁
        return false;
    }
} 