package cn.iocoder.yudao.module.wbclass.service.course;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.UserRespDTO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderPageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderAssignReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderRefundReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseOrderCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseOrderPageReqVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseOrderConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;

import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseOrderMapper;
import cn.iocoder.yudao.module.wbclass.enums.OrderStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.PayStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.RefundStatusEnum;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import cn.iocoder.yudao.module.wbclass.service.progress.WbClassLearningProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.wbclass.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * 练习营课程订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WbClassCourseOrderServiceImpl implements WbClassCourseOrderService {

    @Resource
    private WbClassCourseOrderMapper orderMapper;

    @Resource
    private WbClassCourseService courseService;

    @Resource
    private WbClassCourseProductSkuService productSkuService;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private WbClassLearningProgressService learningProgressService;

    @Override
    public Long createOrder(AppWbClassCourseOrderCreateReqVO createReqVO, Long userId, String userIp) {
        // 校验参数：必须指定courseId或skuId中的一个
        if (createReqVO.getCourseId() == null && createReqVO.getSkuId() == null) {
            throw exception(COURSE_ORDER_PARAM_ERROR, "必须指定课程ID或商品SKU ID");
        }

        // 检查用户是否已购买该商品SKU
        if (createReqVO.getSkuId() != null && hasUserPurchasedProductSku(userId, createReqVO.getSkuId())) {
            throw exception(PRODUCT_SKU_ALREADY_PURCHASED);
        }

        // 获取SKU信息和关联的课程
        WbClassCourseProductSkuDO sku = null;
        WbClassCourseDO course = null;
        Integer originalPrice = 0;
        Integer salePrice = 0;
        String skuName = "默认SKU";

        if (createReqVO.getSkuId() != null) {
            // 如果指定了SKU，使用商品SKU逻辑
            sku = productSkuService.getSku(createReqVO.getSkuId());
            if (sku == null) {
                throw exception(PRODUCT_SKU_NOT_EXISTS);
            }

            // 获取SKU关联的课程列表
            List<WbClassCourseDO> courses = productSkuService.getCoursesBySkuId(createReqVO.getSkuId());
            if (courses.isEmpty()) {
                throw exception(SKU_NO_COURSE_RELATION);
            }

            // 使用第一个关联的课程作为主课程
            course = courses.get(0);
            originalPrice = sku.getOriginalPrice();
            salePrice = sku.getSalePrice();
            skuName = sku.getSkuName();
        } else {
            // 如果没有指定SKU，使用传统的课程逻辑（向后兼容）
            course = courseService.getCourse(createReqVO.getCourseId());
            if (course == null) {
                throw exception(COURSE_NOT_EXISTS);
            }
        }

        // 创建订单
        WbClassCourseOrderDO order = WbClassCourseOrderDO.builder()
                .orderNo(generateOrderNo())
                .userId(userId)
                .userIp(userIp)
                .terminal(4) // APP端
                .courseId(course.getId())
                .courseName(course.getName())
                .productId(sku != null ? sku.getProductId() : null) // 设置产品ID
                .skuId(createReqVO.getSkuId())
                .skuName(skuName)
                .originalPrice(originalPrice)
                .discountPrice(0) // 暂不支持优惠
                .payPrice(salePrice)
                .status(OrderStatusEnum.WAITING_PAYMENT.getStatus())
                .payStatus(PayStatusEnum.UNPAID.getStatus())
                .refundStatus(RefundStatusEnum.NO_NEED.getStatus())
                .userRemark(createReqVO.getUserRemark())
                .build();

        orderMapper.insert(order);
        return order.getId();
    }

    @Override
    public Long createOrderByAdmin(WbClassCourseOrderCreateReqVO createReqVO, Long userId) {
        // 校验参数：必须指定courseId或skuId中的一个
        if (createReqVO.getCourseId() == null && createReqVO.getSkuId() == null) {
            throw exception(COURSE_ORDER_PARAM_ERROR, "必须指定课程ID或商品SKU ID");
        }

        // 检查用户是否已购买该商品SKU
        if (createReqVO.getSkuId() != null && hasUserPurchasedProductSku(userId, createReqVO.getSkuId())) {
            throw exception(PRODUCT_SKU_ALREADY_PURCHASED);
        }

        // 获取SKU信息和关联的课程
        WbClassCourseProductSkuDO sku = null;
        WbClassCourseDO course = null;
        Integer originalPrice = 0;
        Integer salePrice = 0;
        String skuName = "默认SKU";

        if (createReqVO.getSkuId() != null) {
            // 如果指定了SKU，使用商品SKU逻辑
            sku = productSkuService.getSku(createReqVO.getSkuId());
            if (sku == null) {
                throw exception(PRODUCT_SKU_NOT_EXISTS);
            }

            // 获取SKU关联的课程列表
            List<WbClassCourseDO> courses = productSkuService.getCoursesBySkuId(createReqVO.getSkuId());
            if (courses.isEmpty()) {
                throw exception(SKU_NO_COURSE_RELATION);
            }

            // 使用第一个关联的课程作为主课程
            course = courses.get(0);
            originalPrice = sku.getOriginalPrice();
            salePrice = sku.getSalePrice();
            skuName = sku.getSkuName();
        } else {
            // 如果没有指定SKU，使用传统的课程逻辑（向后兼容）
            course = courseService.getCourse(createReqVO.getCourseId());
            if (course == null) {
                throw exception(COURSE_NOT_EXISTS);
            }
        }

        // 创建订单
        WbClassCourseOrderDO order = WbClassCourseOrderDO.builder()
                .orderNo(generateOrderNo())
                .userId(userId)
                .terminal(1) // PC端
                .courseId(course.getId())
                .courseName(course.getName())
                .productId(sku != null ? sku.getProductId() : null) // 设置产品ID
                .skuId(createReqVO.getSkuId())
                .skuName(skuName)
                .originalPrice(originalPrice)
                .discountPrice(0)
                .payPrice(salePrice)
                .status(OrderStatusEnum.WAITING_PAYMENT.getStatus())
                .payStatus(PayStatusEnum.UNPAID.getStatus())
                .refundStatus(RefundStatusEnum.NO_NEED.getStatus())
                .userRemark(createReqVO.getUserRemark())
                .build();

        orderMapper.insert(order);
        return order.getId();
    }

    @Override
    public void updateOrder(WbClassCourseOrderUpdateReqVO updateReqVO) {
        // 校验存在
        validateOrderExists(updateReqVO.getId());
        // 更新
        WbClassCourseOrderDO updateObj = WbClassCourseOrderConvert.INSTANCE.convert(updateReqVO);
        orderMapper.updateById(updateObj);
    }

    @Override
    public void deleteOrder(Long id) {
        // 校验存在
        validateOrderExists(id);
        // 删除
        orderMapper.deleteById(id);
    }

    @Override
    public void payOrderSuccess(String orderNo, Long payOrderId, Integer payChannel) {
        WbClassCourseOrderDO order = getOrderByOrderNo(orderNo);
        if (order == null) {
            throw exception(COURSE_ORDER_NOT_EXISTS);
        }

        // 检查订单状态
        if (!OrderStatusEnum.WAITING_PAYMENT.getStatus().equals(order.getStatus())) {
            throw exception(COURSE_ORDER_ALREADY_PAID);
        }

        // 更新订单状态
        WbClassCourseOrderDO updateOrder = WbClassCourseOrderDO.builder()
                .id(order.getId())
                .status(OrderStatusEnum.PAID.getStatus())
                .payStatus(PayStatusEnum.PAID.getStatus())
                .payTime(LocalDateTime.now())
                .payOrderId(payOrderId)
                .payChannel(payChannel)
                .build();

        orderMapper.updateById(updateOrder);

        // 增加SKU销量（如果订单有SKU）
        if (order.getSkuId() != null) {
            productSkuService.incrementSalesCount(order.getSkuId());
        }

        // 初始化用户学习进度
        try {
            learningProgressService.initializeLearningProgress(order.getId(), order.getSkuId(), order.getUserId());
        } catch (Exception e) {
            // 记录错误但不影响支付成功处理
            log.error("支付成功但学习进度初始化失败: orderId={}, skuId={}, memberId={}",
                    order.getId(), order.getSkuId(), order.getUserId(), e);
        }
    }

    @Override
    public void cancelOrder(Long id, Integer cancelType) {
        WbClassCourseOrderDO order = getOrder(id);
        if (order == null) {
            throw exception(COURSE_ORDER_NOT_EXISTS);
        }

        // 检查订单状态
        if (OrderStatusEnum.CANCELLED.getStatus().equals(order.getStatus())) {
            throw exception(COURSE_ORDER_ALREADY_CANCELLED);
        }

        // 更新订单状态
        WbClassCourseOrderDO updateOrder = WbClassCourseOrderDO.builder()
                .id(id)
                .status(OrderStatusEnum.CANCELLED.getStatus())
                .cancelTime(LocalDateTime.now())
                .cancelType(cancelType)
                .build();

        orderMapper.updateById(updateOrder);
    }

    @Override
    public WbClassCourseOrderDO getOrder(Long id) {
        return orderMapper.selectById(id);
    }

    @Override
    public WbClassCourseOrderDO getOrderByOrderNo(String orderNo) {
        return orderMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<WbClassCourseOrderDO> getOrderList(Collection<Long> ids) {
        return orderMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WbClassCourseOrderDO> getOrderPage(WbClassCourseOrderPageReqVO pageReqVO) {
        return orderMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<WbClassCourseOrderDO> getAppOrderPage(AppWbClassCourseOrderPageReqVO pageReqVO, Long userId) {
        return orderMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                .eq(WbClassCourseOrderDO::getUserId, userId)
                .eqIfPresent(WbClassCourseOrderDO::getStatus, pageReqVO.getStatus())
                .eqIfPresent(WbClassCourseOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .orderByDesc(WbClassCourseOrderDO::getId));
    }

    @Override
    public Long assignOrder(WbClassCourseOrderAssignReqVO assignReqVO) {
        // 通过手机号查询用户
        UserRespDTO user = memberUserApi.getUserByMobile(assignReqVO.getUserMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 校验商品SKU存在并获取SKU信息
        WbClassCourseProductSkuDO sku = productSkuService.getSku(assignReqVO.getSkuId());
        if (sku == null) {
            throw exception(PRODUCT_SKU_NOT_EXISTS);
        }

        // 校验商品ID是否匹配
        if (!sku.getProductId().equals(assignReqVO.getProductId())) {
            throw exception(PRODUCT_SKU_NOT_MATCH_PRODUCT);
        }

        // 获取SKU关联的课程列表
        List<WbClassCourseDO> courses = productSkuService.getCoursesBySkuId(assignReqVO.getSkuId());
        if (courses.isEmpty()) {
            throw exception(SKU_NO_COURSE_RELATION);
        }

        // 检查用户是否已购买该SKU
        if (hasUserPurchasedProductSku(user.getId(), assignReqVO.getSkuId())) {
            throw exception(PRODUCT_SKU_ALREADY_PURCHASED);
        }

        // 计算实际支付金额
        Integer actualPayPrice = assignReqVO.getPayPrice();
        Integer discountPrice = assignReqVO.getDiscountPrice();

        // 使用SKU的价格信息
        Integer originalPrice = sku.getOriginalPrice();
        Integer salePrice = sku.getSalePrice();
        String skuName = sku.getSkuName();

        // 如果没有指定实付金额，使用SKU售价
        if (actualPayPrice == null) {
            actualPayPrice = salePrice;
        }

        // 如果没有指定优惠金额，计算优惠金额
        if (discountPrice == null) {
            discountPrice = originalPrice - actualPayPrice;
            // 确保优惠金额不为负数
            if (discountPrice < 0) {
                discountPrice = 0;
            }
        }

        // 使用第一个关联的课程作为主课程（如果有多个课程关联，可以考虑其他策略）
        WbClassCourseDO primaryCourse = courses.get(0);

        WbClassCourseOrderDO order = WbClassCourseOrderDO.builder()
                .orderNo(generateOrderNo())
                .userId(user.getId())
                .terminal(1) // PC端
                .courseId(primaryCourse.getId())
                .courseName(primaryCourse.getName())
                .productId(assignReqVO.getProductId()) // 设置产品ID
                .skuId(assignReqVO.getSkuId())
                .skuName(skuName)
                .originalPrice(originalPrice)
                .discountPrice(discountPrice)
                .payPrice(actualPayPrice)
                .status(OrderStatusEnum.PAID.getStatus()) // 直接设置为已支付
                .payStatus(PayStatusEnum.PAID.getStatus()) // 直接设置为已支付
                .payTime(LocalDateTime.now()) // 设置支付时间为当前时间
                .payChannel(99) // 99表示线下支付/手动分配
                .refundStatus(RefundStatusEnum.NO_NEED.getStatus())
                .userRemark(assignReqVO.getUserRemark())
                .adminRemark(assignReqVO.getAdminRemark())
                .build();

        orderMapper.insert(order);

        // 增加SKU销量
        productSkuService.incrementSalesCount(order.getSkuId());

        // 初始化用户学习进度
        try {
            learningProgressService.initializeLearningProgress(order.getId(), order.getSkuId(), user.getId());
        } catch (Exception e) {
            // 记录错误但不影响订单分配
            log.error("订单分配成功但学习进度初始化失败: orderId={}, skuId={}, memberId={}",
                    order.getId(), order.getSkuId(), user.getId(), e);
        }

        return order.getId();
    }

    @Override
    public void refundOrder(WbClassCourseOrderRefundReqVO refundReqVO) {
        // 校验订单存在
        WbClassCourseOrderDO order = getOrder(refundReqVO.getOrderId());
        if (order == null) {
            throw exception(COURSE_ORDER_NOT_EXISTS);
        }

        // 校验订单状态，只有已支付的订单才能退款
        if (!OrderStatusEnum.PAID.getStatus().equals(order.getStatus())) {
            throw exception(COURSE_ORDER_STATUS_NOT_SUPPORT_REFUND);
        }

        // 校验支付状态
        if (!PayStatusEnum.PAID.getStatus().equals(order.getPayStatus())) {
            throw exception(COURSE_ORDER_NOT_PAID);
        }

        // 校验退款状态，已退款的订单不能再次退款
        if (RefundStatusEnum.REFUNDED.getStatus().equals(order.getRefundStatus())) {
            throw exception(COURSE_ORDER_ALREADY_REFUNDED);
        }

        // 校验退款金额不能超过实付金额
        if (refundReqVO.getRefundPrice() > order.getPayPrice()) {
            throw exception(COURSE_ORDER_REFUND_PRICE_EXCEED);
        }

        // 更新订单状态为已退款
        WbClassCourseOrderDO updateOrder = WbClassCourseOrderDO.builder()
                .id(refundReqVO.getOrderId())
                .status(OrderStatusEnum.REFUNDED.getStatus()) // 4表示已退款
                .refundStatus(RefundStatusEnum.REFUNDED.getStatus())
                .refundPrice(refundReqVO.getRefundPrice())
                .refundTime(LocalDateTime.now())
                .refundReason(refundReqVO.getRefundReason()) // 使用专门的退款原因字段
                .build();

        orderMapper.updateById(updateOrder);

        // 减少课程销量（如果需要的话）
        // courseService.decrementSalesCount(order.getCourseId());
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 检查用户是否已购买该课程
     * 已购买的条件：存在已支付且未退款的订单
     */
    private boolean hasUserPurchasedCourse(Long userId, Long courseId) {
        WbClassCourseOrderDO existOrder = orderMapper.selectOne(
                new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                        .eq(WbClassCourseOrderDO::getUserId, userId)
                        .eq(WbClassCourseOrderDO::getCourseId, courseId)
                        .isNull(WbClassCourseOrderDO::getSkuId) // 只检查没有指定SKU的课程订单
                        .eq(WbClassCourseOrderDO::getPayStatus, PayStatusEnum.PAID.getStatus())
                        .ne(WbClassCourseOrderDO::getRefundStatus, RefundStatusEnum.REFUNDED.getStatus()) // 排除已退款的订单
        );
        return existOrder != null;
    }

    /**
     * 检查用户是否已购买该课程的特定SKU
     * 已购买的条件：存在已支付且未退款的订单
     */
    private boolean hasUserPurchasedCourseSku(Long userId, Long courseId, Long skuId) {
        WbClassCourseOrderDO existOrder = orderMapper.selectOne(
                new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                        .eq(WbClassCourseOrderDO::getUserId, userId)
                        .eq(WbClassCourseOrderDO::getCourseId, courseId)
                        .eq(WbClassCourseOrderDO::getSkuId, skuId)
                        .eq(WbClassCourseOrderDO::getPayStatus, PayStatusEnum.PAID.getStatus())
                        .ne(WbClassCourseOrderDO::getRefundStatus, RefundStatusEnum.REFUNDED.getStatus()) // 排除已退款的订单
        );
        return existOrder != null;
    }

    /**
     * 检查用户是否已购买该商品SKU
     * 已购买的条件：存在已支付且未退款的订单
     */
    private boolean hasUserPurchasedProductSku(Long userId, Long skuId) {
        WbClassCourseOrderDO existOrder = orderMapper.selectOne(
                new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                        .eq(WbClassCourseOrderDO::getUserId, userId)
                        .eq(WbClassCourseOrderDO::getSkuId, skuId)
                        .eq(WbClassCourseOrderDO::getPayStatus, PayStatusEnum.PAID.getStatus())
                        .ne(WbClassCourseOrderDO::getRefundStatus, RefundStatusEnum.REFUNDED.getStatus()) // 排除已退款的订单
        );
        return existOrder != null;
    }

    private void validateOrderExists(Long id) {
        if (orderMapper.selectById(id) == null) {
            throw exception(COURSE_ORDER_NOT_EXISTS);
        }
    }

}
