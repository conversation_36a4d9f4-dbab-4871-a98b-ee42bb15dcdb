-- ===================================================================
-- 订单课程关联表设计
-- 用于解耦订单与课程的直接关联，支持一个订单关联多个课程
-- ===================================================================

-- 1. 创建订单-课程关联表
DROP TABLE IF EXISTS `edusys_wbclass_order_course_relation`;
CREATE TABLE `edusys_wbclass_order_course_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `course_name` varchar(255) NOT NULL COMMENT '课程名称（冗余字段，便于查询）',
  `sort` int DEFAULT 0 COMMENT '排序（同一订单下课程的排序）',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-正常，2-已删除',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_course` (`order_id`, `course_id`, `deleted`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单课程关联表';

-- 2. 为现有订单表添加备注说明（不修改结构，保持向后兼容）
-- 注意：course_id 和 course_name 字段将逐步废弃，新逻辑通过关联表获取课程信息

-- 3. 创建索引优化查询性能
-- 已在表创建时定义了必要的索引：
-- - uk_order_course: 订单-课程唯一约束，防止重复关联
-- - idx_order_id: 根据订单ID查询关联课程
-- - idx_course_id: 根据课程ID查询关联订单
-- - idx_create_time: 按创建时间排序查询

-- 4. 数据迁移准备（后续执行）
-- 将现有订单表中的课程关联关系迁移到新表中
-- 迁移脚本将在后续步骤中提供

-- ===================================================================
-- 表设计说明
-- ===================================================================
-- 1. 支持一个订单关联多个课程（一对多关系）
-- 2. 通过 sort 字段支持课程排序
-- 3. 冗余存储 course_name 便于查询，减少 JOIN 操作
-- 4. 使用软删除机制，支持数据恢复
-- 5. 唯一约束防止同一订单重复关联同一课程
-- 6. 索引设计优化常见查询场景：
--    - 根据订单查询课程列表
--    - 根据课程查询订单列表
--    - 根据用户查询已购课程（需要 JOIN 订单表）
